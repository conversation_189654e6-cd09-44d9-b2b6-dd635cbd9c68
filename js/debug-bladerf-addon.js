#!/usr/bin/env node

/**
 * Updated debug script for BladeRF Video Addon using configuration object
 * Tests the refactored createIQVideoProcessor function with comprehensive callback verification
 */

const addon = require('../build/Debug/bladerf_addon');

// addon.setLogLevel(11, 1); // LINE_DETECTION_NODE
// addon.setLogLevel(13, 1); // OUTPUT_QUEUE_NODE
// addon.setLogLevel(25, 0); // DEVTOOLS_EXPORT_DATA


// Callback statistics tracking
const callbackStats = {
    onStopCalled: false,
    onErrorCalled: false,
    onFrameCalled: false,
    onEventCalled: false,
    frameCount: 0,
    startTime: Date.now()
};

async function runSingleInstanceTest() {
    try {
        console.log('1. Creating single VideoProcessor instance with configuration object:');
        console.log('   📁 Using samples/recording.wav as data source');

        // Create configuration object with all callbacks using new unified schema
        const config = {
            onStop: (code) => {
                callbackStats.onStopCalled = true;
                console.log(`   🛑 ✅ onStop callback invoked! Code: ${code}`);
            },

            onError: (code, message) => {
                callbackStats.onErrorCalled = true;
                console.log(`   ❌ onError callback invoked! Code: ${code}, Message: ${message}`);
            },

            onFrame: (frame) => {
                callbackStats.onFrameCalled = true;
                callbackStats.frameCount++;
                const elapsed = Date.now() - callbackStats.startTime;

                console.log(`   🎬 ✅ onFrame callback #${callbackStats.frameCount} invoked! (${elapsed}ms elapsed)`);

                if (frame && typeof frame === 'object') {
                    console.log(`      Frame Number: ${frame.frameNumber}`);
                    console.log(`      Dimensions: ${frame.width}x${frame.height}`);
                    console.log(`      Image Buffer Size: ${frame.image ? frame.image.length : 'undefined'} bytes`);

                    // Verify JPEG header if image data exists
                    if (frame.image && frame.image.length > 4) {
                        const header = Array.from(frame.image.slice(0, 4))
                          .map(b => b.toString(16).padStart(2, '0'))
                          .join(' ');
                        console.log(`      JPEG Header: ${header}`);

                        if (frame.image[0] === 0xFF && frame.image[1] === 0xD8) {
                            console.log(`      ✅ Valid JPEG format detected`);
                        } else {
                            console.log(`      ⚠️  Unexpected image format`);
                        }
                    }
                } else {
                    console.log(`      ⚠️  Frame object is invalid: ${typeof frame}`);
                }

                // Stop after receiving a few frames for testing
                if (callbackStats.frameCount >= 300) {
                    console.log(`\n   🛑 Stopping after ${callbackStats.frameCount} frames for testing purposes`);
                    setTimeout(() => {
                        if (videoProcessor) {
                            videoProcessor.stop();
                        }
                    }, 100);
                }
            },

            onEvent: (eventName, payload) => {
                callbackStats.onEventCalled = true;
                console.log(`   📡 ✅ onEvent callback invoked! Event: ${eventName}`);
                if (eventName === 'streamReady') {
                    console.log(`      Stream Type: ${payload.type}`);
                    console.log(`      Source Name: ${payload.sourceName}`);
                    if (payload.type === 'bladerf') {
                        console.log(`      Frequency: ${payload.frequencyHz} Hz`);
                        console.log(`      Sample Rate: ${payload.sampleRateHz} Hz`);
                        console.log(`      Bandwidth: ${payload.bandwidthHz} Hz`);
                        console.log(`      Channel: ${payload.channel}`);
                        console.log(`      Gain Mode: ${payload.gainMode}`);
                        if (payload.manualGainDb !== undefined) {
                            console.log(`      Manual Gain: ${payload.manualGainDb} dB`);
                        }
                    } else if (payload.type === 'wav') {
                        console.log(`      Sample Rate: ${payload.sampleRateHz} Hz`);
                    }
                }
            },

            // Unified stream configuration
            stream: {
                source: 'wav',
                wav: {
                    path: 'samples/recording.wav',
                    loop: true,
                    simulateTiming: true
                }
            }
        };

        // Create the VideoProcessor instance
        console.log('   🔧 Creating VideoProcessor instance with config object...');
        const videoProcessor = addon.createIQVideoProcessor(config);

        if (videoProcessor && typeof videoProcessor === 'object') {
            console.log('   ✅ VideoProcessor instance created successfully');
            console.log('   📦 Returned object type:', typeof videoProcessor);
            console.log('   🔧 Available methods:', Object.getOwnPropertyNames(videoProcessor.__proto__));

            // Wait for callbacks with timeout
            console.log('\n2. Waiting for callbacks (30 second timeout):');
            console.log('   ⏳ Monitoring callback activity...');

            const timeoutPromise = new Promise((resolve) => {
                setTimeout(() => {
                    resolve('timeout');
                }, 30000);
            });

            // const callbackPromise = new Promise((resolve) => {
            //     const checkCallbacks = () => {
            //         console.log(1);
            //         if (callbackStats.onEventCalled && callbackStats.frameCount >= 300) {
            //             resolve('success');
            //         } else {
            //             setTimeout(checkCallbacks, 100);
            //         }
            //     };
            //     checkCallbacks();
            // });

            const result = await Promise.race([timeoutPromise, /*callbackPromise*/ ]);

            console.log('\n3. Callback verification results:');
            console.log(`   🛑 onStop called: ${callbackStats.onStopCalled ? '✅ YES' : '❌ NO'}`);
            console.log(`   ❌ onError called: ${callbackStats.onErrorCalled ? '✅ YES' : '❌ NO'}`);
            console.log(`   🎬 onFrame called: ${callbackStats.onFrameCalled ? '✅ YES' : '❌ NO'}`);
            console.log(`   📡 onEvent called: ${callbackStats.onEventCalled ? '✅ YES' : '❌ NO'}`);
            console.log(`   📊 Total frames received: ${callbackStats.frameCount}`);

            if (result === 'timeout') {
                console.log('\n   ⚠️  TIMEOUT: No sufficient callback activity detected within 30 seconds');
                console.log('   🔍 This indicates a potential issue with the async callback mechanism');
            } else {
                console.log('\n   🎉 SUCCESS: Configuration object callbacks are working correctly!');
            }

            // Clean shutdown
            console.log('\n4. Cleaning up 2:');
            if (videoProcessor && typeof videoProcessor.stop === 'function') {
                const stopResult = videoProcessor.stop();
                console.log(`   🛑 VideoProcessor stopped: ${stopResult ? '✅ SUCCESS' : '⚠️  FAILED'}`);
            }

        } else {
            console.log('   ❌ Failed to create VideoProcessor instance');
            console.log('   🔍 Check if the addon was built correctly and WAV file exists');
        }

    } catch (error) {
        console.error('❌ Test failed with error:', error);
        console.error('Stack trace:', error.stack);
    }
}

async function runMultipleInstanceTest() {
    console.log('\n🔄 Testing multiple VideoProcessor instances:');

    const instances = [];
    const instanceCount = 2;

    try {
        for (let i = 0; i < instanceCount; i++) {
            console.log(`   Creating instance ${i + 1}/${instanceCount}...`);

            const config = {
                onStop: (code) => console.log(`   Instance ${i + 1} onStop: ${code}`),
                onError: (code, message) => console.log(`   Instance ${i + 1} onError: ${code}, ${message}`),
                onFrame: (frame) => console.log(`   Instance ${i + 1} onFrame: frame ${frame.frameNumber}`),
                onEvent: (eventName, payload) => console.log(`   Instance ${i + 1} onEvent: ${eventName}`),
                stream: {
                    source: 'wav',
                    wav: {
                        path: 'samples/recording.wav',
                        loop: true,
                        simulateTiming: true
                    }
                }
            };

            const instance = addon.createIQVideoProcessor(config);
            if (instance) {
                instances.push(instance);
                console.log(`   ✅ Instance ${i + 1} created successfully`);
            } else {
                console.log(`   ❌ Failed to create instance ${i + 1}`);
            }
        }

        // Wait a bit for processing
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Clean up all instances
        console.log('   🧹 Cleaning up instances...');
        instances.forEach((instance, i) => {
            if (instance && typeof instance.stop === 'function') {
                instance.stop();
                console.log(`   ✅ Instance ${i + 1} stopped`);
            }
        });

    } catch (error) {
        console.error('❌ Multiple instance test failed:', error);
    }
}

async function runAllTests() {
    console.log('🚀 Starting comprehensive BladeRF Video Addon tests with configuration object...\n');

    await runSingleInstanceTest();
    // await runMultipleInstanceTest();

    console.log('\n✅ All tests completed!');
}

// Execute tests
runAllTests().catch(console.error);
