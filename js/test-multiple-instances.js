#!/usr/bin/env node

/**
 * Test Multiple VideoProcessor Instances
 * 
 * This test verifies that multiple VideoProcessor instances can be created
 * and managed independently, each with their own C++ VideoProcessor instance.
 */

const addon = require('../build/Release/bladerf_addon');

console.log('🧪 Testing Multiple VideoProcessor Instances\n');

async function testMultipleInstances() {
    try {
        console.log('1. Creating multiple VideoProcessor instances with callbacks...');

        // Define configuration objects for each instance
        const createConfig = (instanceId) => ({
            settings: `demo-${instanceId}`,
            onStop: (code) => console.log(`   🛑 Instance ${instanceId} onStop: ${code}`),
            onFrame: (frame) => console.log(`   🎬 Instance ${instanceId} onFrame: frame ${frame.frameNumber}`),
            onError: (code, message) => console.log(`   ❌ Instance ${instanceId} onError: ${code}, ${message}`)
        });

        const config1 = createConfig(1);
        const config2 = createConfig(2);
        const config3 = createConfig(3);

        // Create 3 instances with their respective configurations
        const processor1 = addon.createIQVideoProcessor(config1);
        const processor2 = addon.createIQVideoProcessor(config2);
        const processor3 = addon.createIQVideoProcessor(config3);
        
        if (!processor1 || !processor2 || !processor3) {
            throw new Error('Failed to create one or more VideoProcessor instances');
        }

        console.log('   ✅ Created 3 VideoProcessor instances with configurations successfully');
        console.log('   📦 Instance 1 type:', typeof processor1);
        console.log('   📦 Instance 2 type:', typeof processor2);
        console.log('   📦 Instance 3 type:', typeof processor3);

        // Wait a moment to see some callback activity
        console.log('\n   ⏳ Waiting for callback activity...');
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Verify each has stop method
        console.log('\n2. Verifying each instance has stop method...');
        if (typeof processor1.stop !== 'function') throw new Error('Instance 1 missing stop method');
        if (typeof processor2.stop !== 'function') throw new Error('Instance 2 missing stop method');
        if (typeof processor3.stop !== 'function') throw new Error('Instance 3 missing stop method');
        console.log('   ✅ All instances have stop method');
        
        // Test stopping instances individually
        console.log('\n3. Testing individual instance stopping...');
        
        const stop1Result = processor1.stop();
        console.log('   ✅ Instance 1 stopped, result:', stop1Result);
        
        const stop2Result = processor2.stop();
        console.log('   ✅ Instance 2 stopped, result:', stop2Result);
        
        const stop3Result = processor3.stop();
        console.log('   ✅ Instance 3 stopped, result:', stop3Result);
        
        // Test stopping already stopped instance
        console.log('\n4. Testing double stop (should return false)...');
        const doubleStopResult = processor1.stop();
        console.log('   ✅ Double stop result:', doubleStopResult, '(expected: false)');
        
        // Test creating instances after stopping others
        console.log('\n5. Testing instance creation after stopping others...');

        const callbacks4 = createCallbacks(4);
        const callbacks5 = createCallbacks(5);

        const processor4 = addon.createIQVideoProcessor(callbacks4.onReady, callbacks4.onFrame, callbacks4.onError);
        const processor5 = addon.createIQVideoProcessor(callbacks5.onReady, callbacks5.onFrame, callbacks5.onError);

        if (!processor4 || !processor5) {
            throw new Error('Failed to create new instances after stopping others');
        }

        console.log('   ✅ Created 2 new instances with callbacks after stopping previous ones');

        // Wait a moment to see callback activity
        await new Promise(resolve => setTimeout(resolve, 500));

        // Clean up
        processor4.stop();
        processor5.stop();
        console.log('   ✅ Cleaned up new instances');
        
        console.log('\n🎉 Multiple instance test with callbacks completed successfully!');
        console.log('📝 Each JavaScript object properly manages its own VideoProcessor instance and callbacks.');
        console.log('🔧 Instance isolation, callback management, and cleanup working correctly.');
        
    } catch (error) {
        console.error('❌ Multiple instance test failed:', error.message);
        process.exit(1);
    }
}

// Run the test
testMultipleInstances();
