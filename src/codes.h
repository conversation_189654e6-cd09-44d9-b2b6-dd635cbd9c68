#pragma once

/**
 * Centralized Error and Stop Codes for BladeRF Video Addon
 * 
 * This header defines all error codes and stop codes used throughout the addon
 * to ensure consistency and eliminate redundant type casting.
 */


/**
 * Stop Codes - Reasons why video processing stopped
 * These codes are emitted via the onStop callback to JavaScript
 */
enum class StopCode : int {
    USER_REQUEST        = 0,    // User called stop() method
    EOF_OR_SOURCE_DONE  = 1,    // End of file or data source completed
    PIPELINE_EXIT       = 2,    // <PERSON><PERSON><PERSON> exited unexpectedly (e.g., overflow)
    ERROR               = 3     // Internal error caused stop
};

/**
 * Error Codes - Specific error conditions
 * These codes are emitted via the onError callback to JavaScript
 * Range: 1001-1999 for addon-specific errors
 */
enum class ErrorCode : int {
    START_FAILED               = 1001,  // VideoProcessor.start() failed
    STREAM_OPEN_FAILED         = 1002,  // Failed to open IQ stream (WAV/BladeRF)
    NODE_RUNNER_ENABLE_FAILED  = 1003,  // Failed to initialize NodeThreadRunner
    PIPELINE_EXIT              = 1004,  // <PERSON><PERSON><PERSON> exited unexpectedly
    INTERNAL_ERROR             = 1999   // General internal error
};

/**
 * Utility functions for code conversion
 */
inline int stopCodeToInt(StopCode code) {
    return static_cast<int>(code);
}

inline int errorCodeToInt(ErrorCode code) {
    return static_cast<int>(code);
}

inline StopCode toStopCode(int code) {
    return static_cast<StopCode>(code);
}

inline ErrorCode toErrorCode(int code) {
    return static_cast<ErrorCode>(code);
}

/**
 * Human-readable descriptions for error codes
 */
inline const char* getErrorMessage(const ErrorCode code) {
    switch (code) {
        case ErrorCode::START_FAILED:
            return "VideoProcessor.start() failed";
        case ErrorCode::STREAM_OPEN_FAILED:
            return "Failed to open IQ stream";
        case ErrorCode::NODE_RUNNER_ENABLE_FAILED:
            return "Failed to initialize NodeThreadRunner";
        case ErrorCode::PIPELINE_EXIT:
            return "Pipeline exited unexpectedly";
        case ErrorCode::INTERNAL_ERROR:
            return "Internal error occurred";
        default:
            return "Unknown error";
    }
}

/**
 * Human-readable descriptions for stop codes
 */
inline const char* getStopMessage(StopCode code) {
    switch (code) {
        case StopCode::USER_REQUEST:
            return "User requested stop";
        case StopCode::EOF_OR_SOURCE_DONE:
            return "End of file or source completed";
        case StopCode::PIPELINE_EXIT:
            return "Pipeline exited";
        case StopCode::ERROR:
            return "Error caused stop";
        default:
            return "Unknown stop reason";
    }
}
