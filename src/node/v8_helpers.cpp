#include "v8_helpers.h"

namespace NodeHelpers {

bool GetBool(v8::Isolate* isolate, v8::Local<v8::Object> obj, const char* key, bool def, bool& out) {
  v8::Local<v8::Value> val;
  if (!obj->Get(isolate->GetCurrentContext(), v8::String::NewFromUtf8(isolate, key).ToLocalChecked()).ToLocal(&val) || val->IsUndefined() || val->IsNull()) {
    out = def; 
    return true;
  }
  if (!val->IsBoolean()) return false;
  out = val.As<v8::Boolean>()->Value();
  return true;
}

bool GetNumber(v8::Isolate* isolate, v8::Local<v8::Object> obj, const char* key, double def, double& out) {
  v8::Local<v8::Value> val;
  if (!obj->Get(isolate->GetCurrentContext(), v8::String::NewFromUtf8(isolate, key).ToLocalChecked()).ToLocal(&val) || val->IsUndefined() || val->IsNull()) {
    out = def; 
    return true;
  }
  if (!val->IsNumber()) return false;
  out = val->NumberValue(isolate->GetCurrentContext()).FromMaybe(def);
  return true;
}

bool GetInt(v8::Isolate* isolate, v8::Local<v8::Object> obj, const char* key, int def, int& out) {
  v8::Local<v8::Value> val;
  if (!obj->Get(isolate->GetCurrentContext(), v8::String::NewFromUtf8(isolate, key).ToLocalChecked()).ToLocal(&val) || val->IsUndefined() || val->IsNull()) {
    out = def; 
    return true;
  }
  if (!val->IsNumber()) return false;
  out = val->Int32Value(isolate->GetCurrentContext()).FromMaybe(def);
  return true;
}

bool GetString(v8::Isolate* isolate, v8::Local<v8::Object> obj, const char* key, std::string& out) {
  v8::Local<v8::Value> val;
  if (!obj->Get(isolate->GetCurrentContext(), v8::String::NewFromUtf8(isolate, key).ToLocalChecked()).ToLocal(&val)) return false;
  if (!val->IsString()) return false;
  v8::String::Utf8Value s(isolate, val);
  out = *s ? *s : "";
  return true;
}

}
