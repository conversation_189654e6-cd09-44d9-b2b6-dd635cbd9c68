#pragma once

#include <v8.h>
#include <string>

namespace NodeHelpers {
/**
 * Safely extract a boolean property from a V8 object.
 * @param isolate V8 isolate
 * @param obj V8 object to read from
 * @param key Property key name
 * @param def Default value if property is missing/undefined/null
 * @param out Output value
 * @return true if successful, false if property exists but is not a boolean
 */
bool GetBool(v8::Isolate* isolate, v8::Local<v8::Object> obj, const char* key, bool def, bool& out);

/**
 * Safely extract a numeric property from a V8 object.
 * @param isolate V8 isolate
 * @param obj V8 object to read from
 * @param key Property key name
 * @param def Default value if property is missing/undefined/null
 * @param out Output value
 * @return true if successful, false if property exists but is not a number
 */
bool GetNumber(v8::Isolate* isolate, v8::Local<v8::Object> obj, const char* key, double def, double& out);

/**
 * Safely extract an integer property from a V8 object.
 * @param isolate V8 isolate
 * @param obj V8 object to read from
 * @param key Property key name
 * @param def Default value if property is missing/undefined/null
 * @param out Output value
 * @return true if successful, false if property exists but is not a number
 */
bool GetInt(v8::Isolate* isolate, v8::Local<v8::Object> obj, const char* key, int def, int& out);

/**
 * Safely extract a string property from a V8 object.
 * @param isolate V8 isolate
 * @param obj V8 object to read from
 * @param key Property key name
 * @param out Output string value
 * @return true if successful, false if property doesn't exist or is not a string
 */
bool GetString(v8::Isolate* isolate, v8::Local<v8::Object> obj, const char* key, std::string& out);

} // namespace VideoDecodingAddon
