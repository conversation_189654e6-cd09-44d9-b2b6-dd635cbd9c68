#pragma once
#include "../../../stream-pipeline/stream_node.h"
#include "../../../types.h"
#include "../line_detection_node_types.h"
#include "../frame_composition_node_types.h"
#include "./partials/frame_canvas.h"
#include <memory>
#include <optional>

namespace IQVideoProcessor::Pipeline {

class FrameCompositionNode final : public SPipeline::StreamNode<LineDetectionEvent, FrameCompositionResult> {
public:
  explicit FrameCompositionNode(SampleRateType sampleRate);
  ~FrameCompositionNode() override;

private:
  bool process(LineDetectionEvent& event) override;

  // Event handler methods
  void handleStandardDetected(VideoStandard standard);
  void handleSyncLock();
  void handleSyncLockLost();
  void handleFrameFieldBegin(size_t frameFieldNumber, bool isTopField);
  void handleFrameFieldEnd(size_t frameFieldNumber, bool isTopField);
  void handleLineReceived(const std::vector<TFloat>& data, size_t dataSize, size_t lineNumber, bool isTopField);
  void handleEqualization(const std::vector<TFloat>& data, size_t dataSize);
  void handleUnknownEvent();

  void processCompleteFrame();

  SampleRateType sampleRate_;
  VideoStandard currentStandard_{UNKNOWN_VIDEO_STANDARD};
  std::unique_ptr<FrameCanvas> frameCanvas_{nullptr};

  // State management
  bool isStandardDetected_{false};
  bool isInterlaced_{false};
  bool isTFF_{true};
  bool isSyncLocked_{false};

  struct FrameInfo {
    size_t initialFrameNumber;
    bool hasTopFieldPart;
    bool hasBottomFieldPart;
  };
  std::optional<FrameInfo> currentProcessingFrame_{std::nullopt};
  size_t frameCounter_{0};

  size_t lineLeftPadding_{0};
  size_t lineRightPadding_{0};
  size_t lineHorizontalPadding_{0};

  size_t equalizationBlackLevelCalcRegionSamples_{0};
  size_t equalizationBlackLevelCalcRegionOffset_{0};
  TFloat equalizationBlackLevel_{0.0f};

  FrameCompositionResult currentCompositionResult_;
};

} // namespace IQVideoProcessor::Pipeline
