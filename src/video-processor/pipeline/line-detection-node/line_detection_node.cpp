#include "./line_detection_node.h"
#include "../../helpers/helpers.h"
#include "logging/logging.h"

#include "./devtools/data_exporter.hpp"

namespace IQVideoProcessor::Pipeline {

// <DEBUGGING> // WARNING! DOESN'T DESIGNED TO WORK WITH PARALLEL INSTANCES
std::vector<TFloat> syncPositionsGraphic;
std::vector<TFloat> orcPositionsGraphic;
// </DEBUGGING>

LineDetectionNode::LineDetectionNode(const SampleRateType sampleRate) : sampleRate_(sampleRate), segmentSyncDetectionFilter_(sampleRate) {
  setRunning();
}

LineDetectionNode::~LineDetectionNode() {
  PipelineComponent::stop();
}

bool LineDetectionNode::process(DemodulatedSegment& segment) {
  if (!running()) return false;

  currentSegment_ = &segment; // Store reference to current segment for callbacks

  auto& filteredSegment = segmentSyncDetectionFilter_.process(segment);
  auto [videoSyncPulses, processingEndPosition] = segmentPulsesDetector_.process(filteredSegment);

  // <DEBUGGING> // WARNING! DOESN'T DESIGNED TO WORK WITH PARALLEL INSTANCES
  if (IS_VERBOSE_ENABLED(DEVTOOLS_EXPORT_DATA)) {
    if (syncPositionsGraphic.size() != segment.totalSamples) {
      syncPositionsGraphic.resize(segment.totalSamples);
      orcPositionsGraphic.resize(segment.totalSamples);
    }
    std::fill(syncPositionsGraphic.begin(), syncPositionsGraphic.end(), 0);
    std::fill(orcPositionsGraphic.begin(), orcPositionsGraphic.end(), 30);
    for (const auto & detected_video_sync_pulse : videoSyncPulses) {
      const auto fallingPos = static_cast<uint32_t>(detected_video_sync_pulse.fallingFrontPosition) + segment.effectiveOffset;
      const auto risingPos = static_cast<uint32_t>(detected_video_sync_pulse.risingFrontPosition) + segment.effectiveOffset;
      const auto centerPos = static_cast<uint32_t>(detected_video_sync_pulse.absCenterPosition) - segment.effectiveStartPosition + segment.effectiveOffset;
      syncPositionsGraphic[fallingPos] = -35;
      syncPositionsGraphic[centerPos] = -20;
      syncPositionsGraphic[risingPos] = -35;
    }
    DevTools::export_debug_data<TFloat>("LDN", "fronts", static_cast<int>(segment.segmentIndex), syncPositionsGraphic.data(), syncPositionsGraphic.size());
    DevTools::export_debug_data<TFloat>("LDN", "original", static_cast<int>(segment.segmentIndex), segment.data.data(), segment.totalSamples);
    DevTools::export_debug_data<TFloat>("LDN", "ave500kHz", static_cast<int>(filteredSegment.segmentIndex), filteredSegment.data.data(), filteredSegment.totalSamples);
  }
  // </DEBUGGING>

  const auto& standardDetectionResult = videoStandardDetector_.processSegmentSyncPulses(videoSyncPulses);
  if (standardDetectionResult.status == VideoStandardDetector::DETECTION_IN_PROGRESS) {
    return running();
  }

  if (standardDetectionResult.status == VideoStandardDetector::DETECTION_FAILED) {
    LOG_WARNING(LINE_DETECTION_NODE, "video standard detection failed");
    if (!standardDetectorRetryCountdown_.running()) {
      standardDetectorRetryCountdown_.reset();
    }
    // Waiting for ~1 second before retrying to detect the standard again
    if (standardDetectorRetryCountdown_.tick(filteredSegment.effectiveSamples)) {
      LOG_WARNING(LINE_DETECTION_NODE, "retrying video standard detection");
      standardDetectorRetryCountdown_.stop(); // just in case
      videoStandardDetector_.reset();
    }
    return running();
  }

  if (standardDetectionResult.status == VideoStandardDetector::DETECTION_COMPLETE) {
    // Successfully detected the standard, no further action needed
    standardDetectorRetryCountdown_.stop();
    initLineDetectionEventTemplate(standardDetectionResult);
    if (!syncOrchestrator_.initialized()) {
      LOG_VERBOSE(VIDEO_PROCESSOR, "video standard detected: " << standardDetectionResult.standard << ", samples per line: " << standardDetectionResult.horizontalLineDuration);
      // Emit STANDARD_DETECTED event immediately when video standard is successfully detected
      currentEvent_.type = LineDetectionEventType::STANDARD_DETECTED;
      currentEvent_.videoStandard = standardDetectionResult.standard;
      currentEvent_.dataSize = 0;
      currentEvent_.lineNumber = 0;
      currentEvent_.frameFieldNumber = 0;
      if (!sendOutput(currentEvent_)) {
        stop();
        return false;
      }
      initSyncOrchestrator(standardDetectionResult); // Initialize the orchestrator only once when standard is first detected
    }
  }

  if (syncOrchestrator_.initialized()) {
    syncOrchestrator_.process(videoSyncPulses, processingEndPosition);
    if (IS_VERBOSE_ENABLED(DEVTOOLS_EXPORT_DATA)) {
      DevTools::export_debug_data<TFloat>("LDN", "orcResults", static_cast<int>(filteredSegment.segmentIndex), orcPositionsGraphic.data(), orcPositionsGraphic.size());
    }
  }

  return running();
}

void LineDetectionNode::initLineDetectionEventTemplate(const VideoStandardDetector::Result& standardDetectionResult) {
  auto measuredLineSize = static_cast<size_t>(standardDetectionResult.horizontalLineDuration);
  measuredLineSize += measuredLineSize >> 2; // Reserve 25% extra space for safety
  if (currentEvent_.data.size() < measuredLineSize) {
    currentEvent_.data.resize(measuredLineSize);
  }
  currentEvent_.videoStandard = standardDetectionResult.standard;
  currentEvent_.type = LineDetectionEventType::UNKNOWN;
  currentEvent_.isTopField = false;
  currentEvent_.dataSize = 0;
  currentEvent_.lineNumber = 0;
  currentEvent_.frameFieldNumber = 0;
}

void LineDetectionNode::initSyncOrchestrator(const VideoStandardDetector::Result& standardDetectionResult) {
  syncOrchestrator_.initialize(standardDetectionResult, [this](const SyncOrchestrator::EventData &event) {
    switch (event.type) {
      case SyncOrchestrator::LINE_DETECTED: {
        currentEvent_.type = LineDetectionEventType::LINE_RECEIVED;
        currentEvent_.isTopField = event.isTopField;
        currentEvent_.lineNumber = event.lineNumber;
        currentEvent_.dataSize = std::ceil(event.toAbsPosition_ - event.fromAbsPosition_);
        // Copy line data from the current segment
        const auto fromIdx = static_cast<uint64_t>(event.fromAbsPosition_) - currentSegment_->effectiveStartPosition + currentSegment_->effectiveOffset;
        std::memcpy(&currentEvent_.data[0], &currentSegment_->data[fromIdx], currentEvent_.dataSize * sizeof(currentSegment_->data[0]));
        LOG_VERBOSE(LINE_DETECTION_NODE, "SO-> line detected: " << (event.isTopField ? "TOP FIELD" : "BOTTOM FIELD") << ", line #" << event.lineNumber << " at " << event.fromAbsPosition_ << " to " << event.toAbsPosition_ << " size " << currentEvent_.dataSize);
        break;
      }
      case SyncOrchestrator::EQUALIZATION_DETECTED: {
        currentEvent_.type = LineDetectionEventType::EQUALIZATION;
        currentEvent_.isTopField = event.isTopField;
        currentEvent_.lineNumber = 0;
        currentEvent_.dataSize = std::ceil(event.toAbsPosition_ - event.fromAbsPosition_);
        // Copy equalization pulse data from the current segment
        const auto fromIdx = static_cast<uint64_t>(event.fromAbsPosition_) - currentSegment_->effectiveStartPosition + currentSegment_->effectiveOffset;
        std::memcpy(&currentEvent_.data[0], &currentSegment_->data[fromIdx], currentEvent_.dataSize * sizeof(currentSegment_->data[0]));
        LOG_VERBOSE(LINE_DETECTION_NODE, "SO-> equalization detected: " << (event.isTopField ? "TOP FIELD" : "BOTTOM FIELD") << " at " << event.fromAbsPosition_ << " to " << event.toAbsPosition_ << " size " << currentEvent_.dataSize);
        break;
      }
      case SyncOrchestrator::FRAME_FIELD_BEGIN:
        currentEvent_.type = LineDetectionEventType::FRAME_FIELD_BEGIN;
        currentEvent_.isTopField = event.isTopField;
        currentEvent_.dataSize = 0;
        currentEvent_.lineNumber = 0;
        currentEvent_.frameFieldNumber = event.uninterruptedFieldSeries;
        LOG_VERBOSE(LINE_DETECTION_NODE, "SO-> frame field begin: " << (event.isTopField ? "TOP FIELD" : "BOTTOM FIELD") << ", " << "uninterrupted fields: " << event.uninterruptedFieldSeries);
        break;
      case SyncOrchestrator::FRAME_FIELD_END:
        currentEvent_.type = LineDetectionEventType::FRAME_FIELD_END;
        currentEvent_.isTopField = event.isTopField;
        currentEvent_.dataSize = 0;
        currentEvent_.lineNumber = 0;
        LOG_VERBOSE(LINE_DETECTION_NODE, "SO-> frame field end: " << (event.isTopField ? "TOP FIELD" : "BOTTOM FIELD") << ", " << "uninterrupted fields: " << event.uninterruptedFieldSeries);
        break;
      case SyncOrchestrator::LOCKED:
        currentEvent_.type = LineDetectionEventType::SYNC_LOCKED;
        currentEvent_.dataSize = 0;
        currentEvent_.lineNumber = 0;
        LOG_VERBOSE(LINE_DETECTION_NODE, "SO-> sync lock acquired");
        break;
      case SyncOrchestrator::LOCK_LOST:
        currentEvent_.type = LineDetectionEventType::SYNC_LOCK_LOST;
        currentEvent_.dataSize = 0;
        currentEvent_.lineNumber = 0;
        LOG_VERBOSE(LINE_DETECTION_NODE, "SO-> sync lock lost");
        break;
      case SyncOrchestrator::UNKNOWN_EVENT:
      default:
        currentEvent_.type = LineDetectionEventType::UNKNOWN;
        currentEvent_.dataSize = 0;
        currentEvent_.lineNumber = 0;
        LOG_ERROR(LINE_DETECTION_NODE, "SO-> unknown event");
        break;
    }

    if (!sendOutput(currentEvent_)) {
      stop();
    }
  });
}

} // namespace IQVideoProcessor::Pipeline
