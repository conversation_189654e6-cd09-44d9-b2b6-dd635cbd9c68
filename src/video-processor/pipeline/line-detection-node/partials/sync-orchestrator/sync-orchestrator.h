#pragma once
#include "./sync_pulse_locator.h"
#include "./sync-encoders/ntsc-encoder.h"
#include "../video_standard_detector.h"
#include "../../../../utils/countdown/countdown.h"
#include <functional>
#include <tuple>

namespace IQVideoProcessor::Pipeline {

using namespace SyncEncoders;

class SyncOrchestrator {
public:
  enum EventType {
    UNKNOWN_EVENT = 0,
    FRAME_FIELD_BEGIN = 10,
    EQUALIZATION_DETECTED = 20,
    LINE_DETECTED = 30,
    FRAME_FIELD_END = 40,
    LOCK_LOST = 50,
    LOCKED = 60,
  };

  struct EventData {
    VideoStandard standard{UNKNOWN_VIDEO_STANDARD};
    EventType type{UNKNOWN_EVENT};
    bool isTopField{true};
    double lineDistance_{0};                // in samples
    double fromAbsPosition_{0};             // absolute position (center-to-center start)
    double toAbsPosition_{0};
    size_t lineNumber{0};                   // number of visible lines detected in the current frame field
    size_t uninterruptedFieldSeries{0};     // number of uninterrupted frame fields since last lock
  };

  using EventCallback = std::function<void(const EventData&)>;

  explicit SyncOrchestrator(SampleRateType sampleRate);

  void initialize(const VideoStandardDetector::Result& detectedStandard, EventCallback eventCallback);
  [[nodiscard]] bool initialized() const;

  void process(const std::vector<VideoSyncPulse>& pulses, double processingEndPosition);
  void reset();

private:
  [[nodiscard]] std::tuple<bool, size_t> tryLockOnVertical(const std::vector<VideoSyncPulse>& pulses) const;
  [[nodiscard]] inline bool canProcessCurrentEncoderItem(double processingEndPosition) const;
  inline void processCurrentEncoderItem(SyncPulseLocator &pulseLocator);
  inline void processDefaultFuture();
  inline void processEncoderTransition(Transition transition, double nextProcessingPosition);
  inline static EstimatedPulseType toEstimatedPulseType(VideoSyncPulseType pulseType) ;

  inline void emitEvent(EventType type, double fromPosition, double toPosition, bool isTopField, size_t lineNumber) const;
  inline void handleLockLoss();
  inline void resetCounters();

private:
  // Config
  SampleRateType sampleRate_;
  EventCallback callback_{};
  VideoStandardDetector::Result detectedStandard_{};
  SignalRangeEstimator rangeEstimator_{sampleRate_};

  // Encoder
  std::unique_ptr<VideoSyncEncoder> videoSyncEncoder_{nullptr};

  // Runtime state
  bool initialized_{false};
  bool locked_{false};
  double currentProcessingPosition_{0.0};
  size_t uninterruptedFrameCount_{0};

  // Out-of-sync tracking counters
  Countdown<> unmatchedPulseCounter_{50, true};   // Triggers lock loss at 50, starts at 0
  Countdown<> matchedPulseCounter_{20, true};  // Resets unmatch counter at 20, starts at 0
};

}
