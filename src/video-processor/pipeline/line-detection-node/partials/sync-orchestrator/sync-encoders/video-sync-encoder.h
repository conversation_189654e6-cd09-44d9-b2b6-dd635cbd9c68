#pragma once
#include "../../../../../../types.h"
#include "../../../../../utils/range/range.h"
#include "../../../../../video_standard.h"
#include "../../video_sync_pulse_type.h"

#include <vector>

namespace IQVideoProcessor::Pipeline::SyncEncoders {

enum class Transition {
  NEXT,
  TO_TOP_FIELD_PRE_EQUALIZING_LAST,
  TO_TOP_FIELD_VERTICAL_START,
  TO_TOP_FIELD_VERTICAL_LAST,
  TO_TOP_FIELD_HORIZONTAL_START,
  TO_BOTTOM_FIELD_HORIZONTAL_START,
};

struct EIFuture {
  VideoSyncPulseType pulseType;
  Range<double> inRange;
  double defaultDistance;
  Transition transition;
};

enum class FrameRegion {
  VERTICAL_SYNC,
  POST_EQUALIZING,
  HORIZONTAL_LINES,
  PRE_EQUALIZING
};

class EncoderItem {
public:
  int32_t id;
  VideoSyncPulseType pulseType;
  size_t defaultFutureIdx;
  std::vector<EIFuture> futures;
  double maxFutureDistance{0};
  bool visible;
  bool topField;
  FrameRegion region{FrameRegion::HORIZONTAL_LINES};
  size_t visibleLineIndex{0}; // Index within visible lines (0-239 for NTSC)

  EncoderItem(
    VideoSyncPulseType pt,
    bool isVisible,
    bool isTopField,
    std::vector<EIFuture> f,
    FrameRegion frameRegion,
    size_t visLineIdx = 0,
    size_t defaultIdx = 0
  );

  [[nodiscard]] inline bool canEstimateFutureByDistance(double distance) const;

private:
  static int32_t globalItemId_;
  static int32_t nextItemId();
  static void resetItemId();
};

class VideoSyncEncoder {
public:
  struct Config {
    VideoStandard standard{UNKNOWN_VIDEO_STANDARD};
    SampleRateType sampleRate;
  };

  explicit VideoSyncEncoder(const Config & config);
  virtual ~VideoSyncEncoder() = default;

  [[nodiscard]] const EncoderItem& currentItem() const;
  [[nodiscard]] const EncoderItem& nextItem() const;
  virtual void transit(Transition transition) = 0;

  void reset();

protected:
  [[nodiscard]] inline size_t getEncoderIdx() const;
  [[nodiscard]] inline size_t getNextEncoderIdx() const;
  void setEncoderIdx(size_t idx);
  [[nodiscard]] const std::vector<EncoderItem>& getEncoders() const;
  [[nodiscard]] std::vector<EncoderItem>& getWritableEncoders();

  void transitNext();

  Config config_;

private:
  size_t encoderIdx_{0};
  std::vector<EncoderItem> encoders_;
};

}
