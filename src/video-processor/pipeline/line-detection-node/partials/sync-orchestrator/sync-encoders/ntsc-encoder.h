#pragma once
#include "./video-sync-encoder.h"

// NTSC standard: https://en.wikipedia.org/wiki/NTSC
// Total lines: 525
// Visible lines: 486
// Frame rate: 29.97 fps
// Horizontal frequency: ~15.734 kHz
// Vertical frequency: ~59.94 Hz (interlaced, so 29.97 frames per second)
// Line duration: ~63.5 µs (1 / 15,734 Hz)
// Horizontal sync pulse: ~4.7 µs
// Equalizing pulses: ~2.3 µs
// Vertical sync pulse: ~27.3 µs

namespace IQVideoProcessor::Pipeline::SyncEncoders {

class NTSCSyncEncoder final : public VideoSyncEncoder {
public:
  explicit NTSCSyncEncoder(const VideoSyncEncoder::Config& config);
  ~NTSCSyncEncoder() override = default;

  void transit(Transition transition) override;

private:
  void fillTopOrBottomFieldVerticalEncoders(size_t &startIdx, size_t &lastIdx, bool isTopField);
  void fillTopFieldPostEqualizingEncoders(size_t &startIdx);
  void fillTopOrBottomFieldHorizontalEncoders(size_t &startIdx, bool isTopField);
  void fillTopOrBottomFieldPreEqualizingEncoders(size_t &startIdx, size_t &lastIdx, bool isTopField);
  void fillBottomFieldPostEqualizingEncoders(size_t &startIdx);

  // Distances in samples
  double lineDistance_;
  double halfLineDistance_;
  // Exotic distances in samples (appear due to processing using center positions of impulse instead of falling fronts)
  double lastVerticalToEqualizingDistance_;
  double lastEqualizingToTopFieldHorizontalDistance_;
  double lastEqualizingToBottomFieldHorizontalDistance_;
  double lastEqualizingToVerticalDistance_;
  double lastBottomFieldHorizontalToEqualingDistance_;
  double lastTopFieldHorizontalToEqualingDistance_;

  // Tolerance
  double lineDistanceTolerance_;
  double halfLineDistanceTolerance_;

  // Encoder indices for transitions
  size_t topFieldVerticalStartIdx_{0};
  size_t topFieldVerticalLastIdx_{0};
  size_t topFieldPostEqualizingStartIdx_{0};
  size_t topFieldHorizontalStartIdx_{0};
  size_t topFieldPreEqualizingStartIdx_{0};
  size_t topFieldPreEqualizingLastIdx_{0};
  size_t bottomFieldVerticalStartIdx_{0};
  size_t bottomFieldVerticalLastIdx_{0};
  size_t bottomFieldPostEqualizingStartIdx_{0};
  size_t bottomFieldHorizontalStartIdx_{0};
  size_t bottomFieldPreEqualizingStartIdx_{0};
  size_t bottomFieldPreEqualizingLastIdx_{0};
};

}