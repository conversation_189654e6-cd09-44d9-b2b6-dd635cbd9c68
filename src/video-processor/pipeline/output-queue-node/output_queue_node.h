#pragma once
#include "../frame_composition_node_types.h"
#include "../../../stream-pipeline/stream_node.h"
#include <functional>
#include <queue>
#include <mutex>
#include <condition_variable>

namespace IQVideoProcessor::Pipeline {

class OutputQueueNode final : public SPipeline::StreamNode<FrameCompositionResult, FrameCompositionResult> {
public:
  explicit OutputQueueNode(size_t maxQueueSize, std::function<bool()> onFrameReceived);
  ~OutputQueueNode() override;

  [[nodiscard]] bool hasNextFrame() const;
  [[nodiscard]] FrameCompositionResult& getNextFrame();

private:
  bool process(FrameCompositionResult& result) override;

  std::function<bool()> onFrameReceived_;
  std::queue<FrameCompositionResult> queue_;
  size_t maxQueueSize_;
  mutable std::mutex queueMutex_;

  // Storage for the last retrieved result to maintain reference validity
  FrameCompositionResult lastRetrievedResult_;

  // DEBUG helper to write frame to file
  static void writeCompositionResultToFile(const FrameCompositionResult& result, size_t fileNumber);
};

} // namespace IQVideoProcessor::Pipeline
