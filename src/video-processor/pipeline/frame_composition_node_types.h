#pragma once

#include "../video_standard.h"
#include <vector>

namespace IQVideoProcessor::Pipeline {

struct FrameCompositionResult {
  std::vector<uint8_t> data;  // JPEG compressed frame data
  size_t dataSize;            // Actual size of JPEG data in bytes
  VideoStandard videoStandard;
  size_t frameNumber{0};
  size_t width{0};
  size_t height{0};
};

} // namespace IQVideoProcessor::Pipeline
