#pragma once

#include "../codes.h"
#include "../iiq-stream/iiq_stream.h"
#include "./pipeline/pipeline.h"
#include <memory>
#include <functional>
#include <thread>
#include <condition_variable>
#include <mutex>
#include <atomic>

namespace IQVideoProcessor {

class VideoProcessor {
public:
  explicit VideoProcessor(std::unique_ptr<IIQStream> stream, std::function<void()> onFrameAvailable, std::function<void(StopCode)> onStopped);
  ~VideoProcessor();

  [[nodiscard]] bool start();
  void stop(StopCode reason);   // Non-blocking stop request; safe from any thread.

  [[nodiscard]] bool hasNextFrame() const;
  [[nodiscard]] Pipeline::FrameCompositionResult& getNextFrame() const;
  [[nodiscard]] int lastStopCode() const;

private:
  bool initialize();

  void beginJoinAndTeardownIfNeeded();

  std::unique_ptr<IIQStream> stream_;
  std::function<void()> onFrameAvailable_;
  std::function<void(StopCode)> onStopped_;

  SampleRateType sampleRate_{0};
  size_t maxVideoLineSamples_{0};

  std::atomic<bool> running_{false};
  std::atomic<bool> joinerStarted_{false};
  std::unique_ptr<std::thread> joinerThread_;

  // Pipeline components
  std::unique_ptr<Pipeline::IQAcquisitionNode> acquisitionNode_;
  std::unique_ptr<Pipeline::IQAcquisitionBridge> acquisitionBridge_;
  std::unique_ptr<Pipeline::IQDemodulationNode> demodNode_;
  std::unique_ptr<Pipeline::IQDemodulationLink> demodPassthrough_;
  std::unique_ptr<Pipeline::LineDetectionNode> lineDetectionNode_;
  std::unique_ptr<Pipeline::LineDetectionLink> lineDetectionPassthrough_;
  std::unique_ptr<Pipeline::FrameCompositionNode> frameCompositionNode_;
  std::unique_ptr<Pipeline::FrameCompositionLink> frameCompositionPassthrough_;
  std::unique_ptr<Pipeline::OutputQueueNode> outputQueueNode_;

  // Worker threads
  std::unique_ptr<std::thread> acquisitionThread_;
  std::unique_ptr<std::thread> pipelineMainThread_;
  std::mutex threadsMutex_;
  std::condition_variable threadsCv_;
  std::atomic<bool> pipelineThreadRunning_{false};

  // Stop reason
  std::atomic<int> stopCode_{-1};

  void connectPipelineNodes();
  void deinitialize();
  void sendStopToPipeline() const;
  void setStopCodeIfUnset(StopCode code);
};

} // namespace IQVideoProcessor
