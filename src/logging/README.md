# Ultra-High-Performance Centralized Logging System

An ultra-high-performance, global logging system for the BladeRF video processing project that manages log levels across all components with zero function call overhead.

## Overview

This logging system provides centralized control over logging verbosity for different components of the BladeRF video processing pipeline. Each component can have its own independent log level setting, allowing fine-grained control over debugging output. The system is optimized for maximum performance by eliminating thread safety overhead and using inline functions for zero function call cost.

## Features

- **Ultra-High-Performance**: No mutex overhead + inline functions for zero function call cost
- **Component-specific**: Different log levels for different system components
- **Convenient Macros**: Easy-to-use logging macros that check levels before evaluation
- **Inline Optimization**: All functions are inline for maximum compiler optimization
- **Global control**: Ability to set all loggers to the same level at once
- **Node.js integration**: Exposed to JavaScript through the Node.js addon
- **Zero dependencies**: Uses only standard C++ library components

## ⚠️ Thread Safety Notice

**This logging system is NOT thread-safe by design for maximum performance.** The application must ensure thread-safe usage at a higher level if multiple threads will be modifying log levels simultaneously. Reading log levels and using the logging macros is safe from multiple threads as long as no thread is modifying the log levels.

## Logger Types

The system supports the following logger categories:

### Core System Components

| Logger Type | ID | Description |
|-------------|----|-----------|
| `GENERAL` | 0 | General system logging |
| `BLADERF_STREAM` | 1 | BladeRF hardware streaming |
| `VIDEO_PROCESSOR` | 2 | Video processing pipeline |
| `SIGNAL_PROCESSING` | 3 | Signal processing algorithms |
| `STREAM_PIPELINE` | 4 | Stream pipeline components |
| `CHUNK_PROCESSOR` | 5 | Chunk processing operations |
| `WAV_STREAM` | 6 | WAV file streaming |
| `NODE_ADDON` | 7 | Node.js addon interface |
| `VIDEO_PROCESSOR_WRAPPER` | 8 | Video synchronization logic |

### Video Processing Pipeline Nodes

| Logger Type | ID | Description |
|-------------|----|-----------|
| `IQ_ACQUISITION_NODE` | 9 | IQ data acquisition from streams |
| `IQ_DEMODULATION_NODE` | 10 | Frequency demodulation processing |
| `LINE_DETECTION_NODE` | 11 | Video line detection and sync processing |
| `FRAME_COMPOSITION_NODE` | 12 | Video frame composition and assembly |
| `OUTPUT_QUEUE_NODE` | 13 | Output queue management |

### Line Detection Components

| Logger Type | ID | Description |
|-------------|----|-----------|
| `SYNC_ORCHESTRATOR` | 14 | Video synchronization orchestration |
| `VIDEO_STANDARD_DETECTOR` | 15 | Video standard detection logic |
| `VIDEO_SYNC_DETECTOR` | 16 | Video sync pulse detection |
| `SIGNAL_RANGE_ESTIMATOR` | 17 | Signal range estimation algorithms |
| `SEGMENT_AVE_FILTER` | 18 | Segment averaging filter |

### Frame Composition Components

| Logger Type | ID | Description |
|-------------|----|-----------|
| `FRAME_CANVAS` | 19 | Frame rendering and canvas operations |

### Signal Processing Algorithms

| Logger Type | ID | Description |
|-------------|----|-----------|
| `FIND_FRONT_ALGORITHMS` | 20 | Front detection algorithms (find-front-frwd, etc.) |
| `SYNC_BY_FRWD` | 21 | Synchronization by forward algorithms |

### Utility Components

| Logger Type | ID | Description |
|-------------|----|-----------|
| `STREAM_FACTORY` | 22 | Stream factory components |
| `CONFIG_HELPERS` | 23 | Configuration helper utilities |
| `THREAD_RUNNER` | 24 | Thread management utilities |

## Log Levels

Three log levels are supported:

| Level | Value | Description |
|-------|-------|-------------|
| `NONE` | 0 | No logging output |
| `WARNING` | 1 | Only warnings and errors |
| `VERBOSE` | 2 | Detailed logging information |

## C++ API

### Include the Header

```cpp
#include "logging/logging.h"
```

### High-Performance Logging Macros (Recommended)

The most efficient way to use the logging system is through the provided macros:

```cpp
// Basic logging macros - check level before evaluation
LOG_WARNING(VIDEO_PROCESSOR, "Processing error: " << errorCode);
LOG_VERBOSE(BLADERF_STREAM, "Received " << sampleCount << " samples");
LOG_NONE(GENERAL, "Critical system error: " << message);

// Unconditional error logging - always outputs to stderr
LOG_ERROR(IQ_ACQUISITION_NODE, "Hardware failure detected: " << errorDetails);
LOG_ERROR(SYNC_ORCHESTRATOR, "Sync lock lost unexpectedly");

// Conditional logging - for performance-critical code
LOG_WARNING_IF(SIGNAL_PROCESSING, errorOccurred, "Signal processing failed");
LOG_VERBOSE_IF(CHUNK_PROCESSOR, debugMode, "Chunk " << chunkId << " processed");

// Manual level checking for complex logic
if (IS_VERBOSE_ENABLED(VIDEO_PROCESSOR)) {
    // Expensive computation only if verbose logging is enabled
    std::string detailedInfo = generateDetailedReport();
    std::cout << "[VIDEO_PROCESSOR][VERBOSE] " << detailedInfo << std::endl;
}
```

### Basic API Functions

```cpp
// Set log level for a specific component
Logging::setLogLevel(Logging::LoggerType::VIDEO_PROCESSOR, Logging::LogLevel::VERBOSE);

// Get current log level
Logging::LogLevel level = Logging::getLogLevel(Logging::LoggerType::VIDEO_PROCESSOR);

// Check if a log level is enabled
if (Logging::isLogLevelEnabled(Logging::LoggerType::VIDEO_PROCESSOR, Logging::LogLevel::WARNING)) {
    // Log warning message
}

// Set all loggers to the same level
Logging::setGlobalLogLevel(Logging::LogLevel::WARNING);

// Reset all loggers to default (WARNING)
Logging::resetLogLevels();
```

### Performance Considerations

- **Use macros**: The logging macros are optimized to check log levels before evaluating expressions
- **No thread safety overhead**: Direct memory access for maximum speed
- **Minimal function call overhead**: Inline functions where possible
- **Conditional evaluation**: Expensive logging expressions are only evaluated when needed

## Node.js API

The logging system is exposed to JavaScript through the `setLogLevel` function:

```javascript
const addon = require('./build/Debug/bladerf_addon.node');

// Set GENERAL logger to VERBOSE level
addon.setLogLevel(0, 2); // Returns true on success

// Set BLADERF_STREAM logger to NONE level  
addon.setLogLevel(1, 0); // Returns true on success

// Error handling
try {
    addon.setLogLevel(999, 1); // Invalid logger type
} catch (error) {
    console.error('Error:', error.message);
}
```

### JavaScript Constants

For better readability, you can define constants:

```javascript
const LoggerType = {
    // Core system components
    GENERAL: 0,
    BLADERF_STREAM: 1,
    VIDEO_PROCESSOR: 2,
    SIGNAL_PROCESSING: 3,
    STREAM_PIPELINE: 4,
    CHUNK_PROCESSOR: 5,
    WAV_STREAM: 6,
    NODE_ADDON: 7,
    VIDEO_PROCESSOR_WRAPPER: 8,

    // Video processing pipeline nodes
    IQ_ACQUISITION_NODE: 9,
    IQ_DEMODULATION_NODE: 10,
    LINE_DETECTION_NODE: 11,
    FRAME_COMPOSITION_NODE: 12,
    OUTPUT_QUEUE_NODE: 13,

    // Line detection components
    SYNC_ORCHESTRATOR: 14,
    VIDEO_STANDARD_DETECTOR: 15,
    VIDEO_SYNC_DETECTOR: 16,
    SIGNAL_RANGE_ESTIMATOR: 17,
    SEGMENT_AVE_FILTER: 18,

    // Frame composition components
    FRAME_CANVAS: 19,

    // Signal processing algorithms
    FIND_FRONT_ALGORITHMS: 20,
    SYNC_BY_FRWD: 21,

    // Utility components
    STREAM_FACTORY: 22,
    CONFIG_HELPERS: 23,
    THREAD_RUNNER: 24
};

const LogLevel = {
    NONE: 0,
    WARNING: 1,
    VERBOSE: 2
};

// Usage examples
addon.setLogLevel(LoggerType.VIDEO_PROCESSOR, LogLevel.VERBOSE);
addon.setLogLevel(LoggerType.IQ_ACQUISITION_NODE, LogLevel.WARNING);
addon.setLogLevel(LoggerType.SYNC_ORCHESTRATOR, LogLevel.NONE);
```

## Available Logging Macros

### Basic Logging Macros

| Macro | Description | Example |
|-------|-------------|---------|
| `LOG_NONE(logger_type, message)` | Log at NONE level | `LOG_NONE(GENERAL, "Critical error")` |
| `LOG_WARNING(logger_type, message)` | Log at WARNING level | `LOG_WARNING(BLADERF_STREAM, "Device timeout")` |
| `LOG_VERBOSE(logger_type, message)` | Log at VERBOSE level | `LOG_VERBOSE(VIDEO_PROCESSOR, "Frame processed")` |
| `LOG_ERROR(logger_type, message)` | **Unconditional error logging to stderr** | `LOG_ERROR(IQ_ACQUISITION_NODE, "Critical failure")` |

### Conditional Logging Macros

| Macro | Description | Example |
|-------|-------------|---------|
| `LOG_WARNING_IF(logger_type, condition, message)` | Log warning if condition is true | `LOG_WARNING_IF(GENERAL, error, "Error occurred")` |
| `LOG_VERBOSE_IF(logger_type, condition, message)` | Log verbose if condition is true | `LOG_VERBOSE_IF(SIGNAL_PROCESSING, debug, "Debug info")` |

### Level Check Macros

| Macro | Description | Example |
|-------|-------------|---------|
| `IS_LOG_ENABLED(logger_type, log_level)` | Check if specific level is enabled | `IS_LOG_ENABLED(GENERAL, WARNING)` |
| `IS_WARNING_ENABLED(logger_type)` | Check if WARNING level is enabled | `IS_WARNING_ENABLED(VIDEO_PROCESSOR)` |
| `IS_VERBOSE_ENABLED(logger_type)` | Check if VERBOSE level is enabled | `IS_VERBOSE_ENABLED(CHUNK_PROCESSOR)` |

### Macro Usage Examples

```cpp
// Simple logging with stream operators
LOG_WARNING(BLADERF_STREAM, "Failed to read " << sampleCount << " samples");

// Unconditional error logging - always outputs regardless of log level
LOG_ERROR(IQ_ACQUISITION_NODE, "Critical hardware failure: " << errorCode);
LOG_ERROR(FRAME_COMPOSITION_NODE, "Frame buffer overflow detected");

// Conditional logging to avoid expensive operations
LOG_VERBOSE_IF(VIDEO_PROCESSOR, frameCount % 100 == 0,
               "Processed " << frameCount << " frames");

// Manual control for complex scenarios
if (IS_VERBOSE_ENABLED(SIGNAL_PROCESSING)) {
    auto stats = calculateExpensiveStatistics();
    LOG_VERBOSE(SIGNAL_PROCESSING, "Statistics: " << stats.toString());
}
```

### LOG_ERROR Macro - Special Behavior

The `LOG_ERROR` macro has special behavior that differs from other logging macros:

- **Unconditional Output**: Always outputs to stderr regardless of the logger's configured log level
- **Error Stream**: Uses `std::cerr` instead of `std::cout` for proper error handling
- **Critical Errors**: Intended for critical errors that must always be visible
- **No Level Check**: Does not check `isLogLevelEnabled()` - always executes

```cpp
// These will ALWAYS output to stderr, regardless of log level settings
LOG_ERROR(SYNC_ORCHESTRATOR, "Sync lock completely lost - recovery impossible");
LOG_ERROR(BLADERF_STREAM, "Hardware disconnected unexpectedly");

// Even if the logger is set to NONE, LOG_ERROR still outputs
Logging::setLogLevel(Logging::LoggerType::IQ_ACQUISITION_NODE, Logging::LogLevel::NONE);
LOG_ERROR(IQ_ACQUISITION_NODE, "This will still be printed to stderr");
```

## Implementation Details

### File Structure

- `logging.h` - Header file with declarations and inline functions
- `logging.cpp` - Implementation file with function definitions
- `.gitignore` - Ignore build artifacts

### Memory Layout

- Global array of log levels (one per logger type)
- Direct memory access with no synchronization overhead
- Static initialization ensures proper startup
- Global array defined in .cpp file to avoid multiple definition errors
- External declaration in header allows inline functions to access global state

### Inline Function Implementation

- **All functions are inline**: `setLogLevel()`, `getLogLevel()`, `isLogLevelEnabled()`, etc.
- **Zero function call overhead**: Compiler can optimize calls completely away
- **Header-only interface**: Function implementations in `logging.h` for maximum optimization
- **Proper linkage**: Global array remains in `logging.cpp` to prevent multiple definitions
- **Compiler-friendly**: Enables aggressive optimization and dead code elimination

### Performance Optimizations

- **Zero mutex overhead**: Direct memory access for maximum speed
- **Inline functions**: All logging functions are inline for zero function call overhead
- **Compiler optimization**: Functions can be completely optimized away when disabled
- **Macro-based logging**: Level checks before expensive expression evaluation
- **No dynamic allocation**: All memory is statically allocated
- **Measured performance**: 1M log level checks in 0 microseconds (completely optimized away)

## Build Integration

The logging system is automatically built with both CMake and node-gyp:

### CMakeLists.txt
```cmake
# Already included in source list
src/logging/logging.cpp
```

### binding.gyp
```json
{
  "sources": [
    "src/logging/logging.cpp"
  ]
}
```

## Default Behavior

- All loggers start with `WARNING` level by default
- Static initialization occurs automatically
- No configuration required for basic usage
- Ultra-high-performance operation out of the box with inline functions

## Error Handling

The Node.js interface provides comprehensive error checking:

- Parameter count validation
- Parameter type validation  
- Range checking for logger types and log levels
- Descriptive error messages for debugging
