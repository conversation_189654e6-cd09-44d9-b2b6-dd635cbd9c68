#include "iq_stream_factory.h"
#include "../wav-stream/wav_stream.h"
#include "../bladerf-stream/bladerf_stream.h"
#include "../video-processor/video_processor_configs.h"
#include "logging/logging.h"

std::tuple<std::unique_ptr<IIQStream>, std::string>IQStreamFactory::createWAVStream(IQStreamConfigHelpers::WAVStreamConfig& config) {
  LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "creating WAV IQStream; path=" << config.path << ", loop=" << config.loop << ", timing=" << config.simulateTiming);

  auto wav = std::make_unique<WAVIQStream>(config.path, config.loop, config.simulateTiming);
  if (!wav->open()) {
    LOG_ERROR(WAV_STREAM, "failed to open WAV file: " << wav->lastError());
    return {nullptr, "failed to open WAV file: " + wav->lastError()};
  }

  // WAV streams use exact parameters from file, no modification needed
  return {std::move(wav), ""};
}

std::tuple<std::unique_ptr<IIQStream>, std::string>IQStreamFactory::createBladeRFStream(IQStreamConfigHelpers::BladeRFStreamConfig& config) {
  BladeRFIQStream::Config cfg;
  cfg.frequencyHz = config.frequencyHz;
  cfg.sampleRateHz = config.sampleRateHz;
  cfg.bandwidthHz = config.bandwidthHz;
  cfg.channel = config.channel;
  cfg.gainMode = config.gainMode;
  cfg.manualGainDb = config.manualGainDb;

  BladeRFIQStream::BufferConfig bufCfg;
  bufCfg.bufferSize = IQ_STREAM_READ_SIZE;
  bufCfg.numBuffers = 16;
  bufCfg.numTransfers = 8;

  LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "creating BladeRF IQStream; "
    << "freq=" << cfg.frequencyHz << "Hz, rate=" << cfg.sampleRateHz
    << "Hz, bw=" << cfg.bandwidthHz << "Hz, ch=" << cfg.channel
    << ", gainMode=" << (cfg.gainMode == BladeRFIQStream::GainMode::MANUAL ? "manual" : "auto")
    << (cfg.gainMode == BladeRFIQStream::GainMode::MANUAL ? (", gainDb=" + std::to_string(cfg.manualGainDb)) : ""));

  auto brf = std::make_unique<BladeRFIQStream>(cfg, bufCfg);
  if (!brf->open()) {
    LOG_ERROR(BLADERF_STREAM, "failed to open bladeRF: " << brf->lastError());
    return {nullptr, "failed to open bladeRF: " + brf->lastError()};
  }

  // Update config with resolved parameters from hardware
  const auto info = brf->getResolvedInfo();
  config.frequencyHz = info.frequencyHz;
  config.sampleRateHz = info.sampleRateHz;
  config.bandwidthHz = info.bandwidthHz;
  config.channel = info.channel;
  config.gainMode = info.gainMode;
  config.manualGainDb = info.manualGainDb;

  return {std::move(brf), ""};
}
