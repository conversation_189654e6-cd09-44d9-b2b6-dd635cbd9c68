#ifndef IQ_STREAM_FACTORY_H
#define IQ_STREAM_FACTORY_H

#include "../iiq-stream/iiq_stream.h"
#include "iq_stream_config_helpers.h"
#include <memory>
#include <string>
#include <tuple>

class IQStreamFactory {
public:
  struct StreamResult {
    std::unique_ptr<IIQStream> stream;
    std::string error;
  };

  static std::tuple<std::unique_ptr<IIQStream>, std::string> createWAVStream(IQStreamConfigHelpers::WAVStreamConfig& config);
  static std::tuple<std::unique_ptr<IIQStream>, std::string> createBladeRFStream(IQStreamConfigHelpers::BladeRFStreamConfig& config);
};

#endif // IQ_STREAM_FACTORY_H
